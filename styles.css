* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #2e373f;
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
header {
    background: rgba(46, 55, 63, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    padding: 15px 0;
    transition: all 0.3s ease;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 28px;
    font-weight: bold;
}

.logo .crowd {
    color: white;
}

.logo .snap {
    color: #add929;
}

nav ul {
    display: flex;
    list-style: none;
    gap: 30px;
}

nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

nav a:hover {
    color: #add929;
}

nav a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #add929, #ffffff);
    transition: width 0.3s ease;
}

nav a:hover::after {
    width: 100%;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    min-height: 80vh;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 30px;
    background: linear-gradient(135deg, #ffffff 0%, #add929 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.2;
}

.hero-text .subtitle {
    font-size: 1.3rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 50px;
    font-weight: 300;
}

/* Hero Mosaic Gallery */
.hero-mosaic-gallery {
    width: 600px;
    height: 400px;
    position: relative;
    margin: 0 auto;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.gallery-layout {
    width: 100%;
    height: 100%;
    position: relative;
}

.gallery-item {
    position: absolute;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.4);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 3px solid rgba(173, 217, 41, 0.3);
    animation: slideInScale 0.8s ease-out forwards;
    opacity: 0;
    transform: scale(0.8) translateY(30px);
}

@keyframes slideInScale {
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Left corner image */
.gallery-item.large-left {
    width: 200px;
    height: 150px;
    top: 20px;
    left: 20px;
    z-index: 2;
    border-radius: 15px;
    animation-delay: 0.2s;
}

/* Right corner images (close together) */
.gallery-item.large-right {
    width: 180px;
    height: 130px;
    top: 30px;
    right: 30px;
    z-index: 2;
    border-radius: 15px;
    animation-delay: 0.4s;
}

.gallery-item.large-right-second {
    width: 160px;
    height: 110px;
    top: 180px;
    right: 50px;
    z-index: 1;
    border-radius: 15px;
    animation-delay: 0.5s;
}

/* Bottom left corner image */
.gallery-item.bottom-left {
    width: 170px;
    height: 120px;
    bottom: 20px;
    left: 40px;
    z-index: 1;
    border-radius: 15px;
    animation-delay: 0.3s;
}

/* Central feature image (properly centered) */
.gallery-item.center-feature {
    width: 280px;
    height: 200px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
    border-radius: 20px;
    border: 3px solid rgba(173, 217, 41, 0.6);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
    animation-delay: 0.6s;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.9);
    transition: all 0.4s ease;
    animation: imageZoom 0.8s ease-out;
}

@keyframes imageZoom {
    from {
        transform: scale(1.2);
        filter: brightness(0.7);
    }
    to {
        transform: scale(1);
        filter: brightness(0.9);
    }
}

.gallery-item:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.5);
    border-color: #add929;
}

.gallery-item.center-feature:hover {
    transform: translate(-50%, -50%) translateY(-8px) scale(1.05);
}

@keyframes pulse {
    0%, 100% {
        box-shadow: 0 25px 60px rgba(0, 0, 0, 0.5), 0 0 0 0 rgba(173, 217, 41, 0.4);
    }
    50% {
        box-shadow: 0 25px 60px rgba(0, 0, 0, 0.5), 0 0 0 10px rgba(173, 217, 41, 0);
    }
}

.gallery-item:hover img {
    filter: brightness(1.1);
    transform: scale(1.05);
}

.gallery-item.feature-card:hover {
    transform: translateY(-8px) scale(1.05) rotate(0deg);
    animation: wiggle 0.5s ease-in-out;
}

@keyframes wiggle {
    0%, 100% { transform: translateY(-8px) scale(1.05) rotate(0deg); }
    25% { transform: translateY(-8px) scale(1.05) rotate(1deg); }
    75% { transform: translateY(-8px) scale(1.05) rotate(-1deg); }
}

.overlay-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.85));
    color: white;
    padding: 25px;
    text-align: left;
    animation: slideUpFade 1s ease-out 1s both;
}

@keyframes slideUpFade {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.main-feature .overlay-content h3 {
    font-size: 32px;
    font-weight: 800;
    margin-bottom: 12px;
    color: #add929;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    animation: textGlow 2s ease-in-out infinite alternate;
}

@keyframes textGlow {
    from {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7), 0 0 10px rgba(173, 217, 41, 0.3);
    }
    to {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7), 0 0 20px rgba(173, 217, 41, 0.6);
    }
}

/* Add floating animation */
.gallery-item.main-feature {
    animation: slideInScale 0.8s ease-out forwards, float 6s ease-in-out infinite 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.gallery-item.feature-card {
    animation-fill-mode: forwards;
}

.gallery-item.feature-card.left {
    animation: slideInScaleRotateLeft 0.8s ease-out forwards, floatLeft 4s ease-in-out infinite 3s;
}

.gallery-item.feature-card.center {
    animation: slideInScaleRotateCenter 0.8s ease-out forwards, floatCenter 5s ease-in-out infinite 3.5s;
}

.gallery-item.feature-card.right {
    animation: slideInScaleRotateRight 0.8s ease-out forwards, floatRight 4.5s ease-in-out infinite 4s;
}

@keyframes floatLeft {
    0%, 100% { transform: rotate(-2deg) translateY(0px); }
    50% { transform: rotate(-2deg) translateY(-8px); }
}

@keyframes floatCenter {
    0%, 100% { transform: rotate(1deg) translateY(0px); }
    50% { transform: rotate(1deg) translateY(-6px); }
}

@keyframes floatRight {
    0%, 100% { transform: rotate(-1deg) translateY(0px); }
    50% { transform: rotate(-1deg) translateY(-7px); }
}

/* Add glassmorphism effect */
.gallery-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    z-index: 1;
    pointer-events: none;
}

.overlay-content {
    z-index: 2;
}

/* CTA Buttons */
.cta-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, #add929 0%, #2e373f 100%);
    color: white;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Platform Section */
.platform-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 100px 0;
    position: relative;
}

.platform-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.platform-image {
    position: relative;
}

.platform-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.platform-text {
    padding-left: 40px;
}

.platform-text .section-title {
    text-align: left;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.platform-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #2e373f;
}

@media (max-width: 768px) {
    .platform-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
    
    .platform-text {
        padding-left: 0;
    }
    
    .platform-text .section-title {
        text-align: center;
    }
}

/* Features Section */
.features-section {
    background: white;
    padding: 100px 0;
    color: #2e373f;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 60px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.feature-card {
    background: white;
    border-radius: 15px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(46, 55, 63, 0.1);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(46, 55, 63, 0.15);
    border-color: #add929;
}

.feature-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 15px 15px 0 0;
}

.feature-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.feature-card:hover .feature-image img {
    transform: scale(1.05);
}

.feature-content {
    padding: 25px;
}

.feature-card h3 {
    color: #2e373f;
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.feature-card .arrow {
    color: #add929;
    font-size: 1.3rem;
    font-weight: bold;
    margin-left: 10px;
}

.feature-card p {
    color: #2e373f;
    line-height: 1.6;
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

@media (max-width: 768px) {
    .features-grid {
        grid-template-columns: 1fr;
    }
}

/* Gallery Section */
.gallery-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    padding: 100px 0;
}

.gallery-section .section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2e373f;
    margin-bottom: 60px;
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.gallery-container {
    position: relative;
    width: 100%;
    margin: 40px auto 0;
    overflow: hidden;
}

.gallery-grid {
    display: flex;
    gap: 30px;
    animation: scroll-horizontal 20s linear infinite;
    width: calc(8 * 380px + 7 * 30px); /* Double the cards for seamless loop */
}

@keyframes scroll-horizontal {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(calc(-4 * 380px - 3 * 30px)); /* Move by original set width */
    }
}

.gallery-grid:hover {
    animation-play-state: paused;
}

.gallery-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
}

.gallery-nav {
    background: rgba(46, 55, 63, 0.8);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gallery-nav:hover {
    background: #add929;
    transform: scale(1.1);
}

.gallery-dots {
    display: flex;
    gap: 10px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ccc;
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: #add929;
    transform: scale(1.2);
}

.gallery-card {
    background: white;
    border-radius: 15px;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(46, 55, 63, 0.1);
    width: 350px;
    flex-shrink: 0;
}

.gallery-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(46, 55, 63, 0.15);
    border-color: #add929;
}

.gallery-image {
    width: 100%;
    height: 250px;
    overflow: hidden;
    border-radius: 15px;
}

.gallery-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-card:hover .gallery-image img {
    transform: scale(1.05);
}

.gallery-content {
    padding: 25px;
}

.gallery-card h3 {
    color: #2e373f;
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
}

.gallery-card p {
    color: #2e373f;
    line-height: 1.6;
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.8;
}

@media (max-width: 768px) {
    .gallery-card {
        min-width: 280px;
    }
    
    .gallery-nav {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
}

/* Testimonials */
.testimonials-section {
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    padding: 100px 0;
    color: white;
}

.testimonials-section .section-title {
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    font-weight: 700;
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: white;
    background-clip: unset;
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 60px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.testimonial-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    border-color: #add929;
}

.testimonial-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    border-radius: 15px 15px 0 0;
}

.testimonial-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.testimonial-card:hover .testimonial-image img {
    transform: scale(1.05);
}

.testimonial-content {
    padding: 25px;
}

.testimonial-card h3 {
    color: white;
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.testimonial-card .arrow {
    color: #add929;
    font-size: 1.3rem;
    font-weight: bold;
    margin-left: 10px;
}

.testimonial-card p {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    margin: 0;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
}

/* Trusted Organizations Section */
.trusted-section {
    background: white;
    padding: 60px 0;
    text-align: center;
}

.trusted-section .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2e373f;
    margin-bottom: 60px;
    text-align: center;
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: #2e373f;
    background-clip: unset;
}

.logos-grid {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 60px;
    flex-wrap: wrap;
    max-width: 1000px;
    margin: 0 auto;
}

.logo-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 80px;
}

.logo-item img {
    width: 130px;
    height: 60px;
    object-fit: contain;
}

@media (max-width: 768px) {
    .logos-grid {
        gap: 40px;
    }
    
    .logo-item {
        width: 120px;
        height: 60px;
    }
    
    .logo-item img {
        width: 100px;
        height: 45px;
    }
    
    .trusted-section .section-title {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .logos-grid {
        gap: 30px;
    }
    
    .logo-item {
        width: 100px;
        height: 50px;
    }
    
    .logo-item img {
        width: 85px;
        height: 35px;
    }
}

/* Footer */
footer {
    background: linear-gradient(135deg, #000000 0%, #2e373f 100%);
    padding: 60px 0 30px;
    color: white;
    text-align: center;
}

.footer-cta {
    margin-bottom: 40px;
}

.footer-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #add929 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
    margin-top: 30px;
}

/* Section Titles - Global Styling */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 60px;
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Features Section */
.features-section {
    background: white;
    padding: 100px 0;
    color: #2e373f;
}

/* Testimonials Section */
.testimonials-section .section-title {
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    font-weight: 700;
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: white;
    background-clip: unset;
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
}

/* Platform Section */
.platform-section .section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 60px;
    background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Trusted Organizations Section */
.trusted-section .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2e373f;
    margin-bottom: 60px;
    text-align: center;
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: #2e373f;
    background-clip: unset;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content,
    .platform-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .exhibition-visual {
        width: 300px;
        height: 200px;
    }

    nav ul {
        display: none;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }
}

/* Scroll animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Hero Mosaic Gallery */
.hero-mosaic-gallery {
    width: 700px;
    height: 400px;
    position: relative;
}

.gallery-layout {
    width: 100%;
    height: 100%;
    position: relative;
}

.gallery-item {
    position: absolute;
    border-radius: 25px;
    overflow: hidden;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.4);
    transition: all 0.4s ease;
    border: 3px solid rgba(173, 217, 41, 0.3);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.9);
    transition: all 0.4s ease;
}

.gallery-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 60px rgba(0, 0, 0, 0.5);
    border-color: #add929;
}

.gallery-item:hover img {
    filter: brightness(1.1);
    transform: scale(1.05);
}

.gallery-item.large-left {
    width: 320px;
    height: 380px;
    top: 10px;
    left: 0;
    z-index: 1;
    border-radius: 20px;
    animation-delay: 0.2s;
}

.gallery-item.large-right {
    width: 320px;
    height: 380px;
    top: 10px;
    right: 0;
    z-index: 1;
    border-radius: 20px;
    animation-delay: 0.4s;
}

.gallery-item.center-feature {
    width: 280px;
    height: 200px;
    top: 100px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    border-radius: 15px;
    border: 4px solid white;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
    animation-delay: 0.6s;
}

.gallery-item.feature-card:hover {
    transform: translateY(-8px) scale(1.05) rotate(0deg);
}

.overlay-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.85));
    color: white;
    padding: 25px;
    text-align: left;
}

.main-feature .overlay-content {
    padding: 30px;
}

.main-feature .overlay-content h3 {
    font-size: 32px;
    font-weight: 800;
    margin-bottom: 12px;
    color: #add929;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.main-feature .overlay-content p {
    font-size: 20px;
    opacity: 0.95;
    margin: 0;
    font-weight: 400;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
}

.feature-card .overlay-content {
    padding: 18px;
    text-align: center;
}

.feature-card .overlay-content h4 {
    font-size: 18px;
    font-weight: 700;
    margin: 0;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

/* Add glassmorphism effect */
.gallery-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    z-index: 1;
    pointer-events: none;
}

.overlay-content {
    z-index: 2;
}











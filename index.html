<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CrowdSnap - AI-Powered Event Management</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #2e373f;
            background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: rgba(46, 55, 63, 0.95);
            backdrop-filter: blur(10px);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            padding: 15px 0;
            transition: all 0.3s ease;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #add929, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        nav ul {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        nav a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }

        nav a:hover {
            color: #add929;
        }

        nav a::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(45deg, #add929, #ffffff);
            transition: width 0.3s ease;
        }

        nav a:hover::after {
            width: 100%;
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            background: linear-gradient(135deg, 
                rgba(46, 55, 63, 0.9) 0%, 
                rgba(0, 0, 0, 0.8) 50%, 
                rgba(173, 217, 41, 0.2) 100%),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="gradient" cx="50%" cy="50%" r="50%"><stop offset="0%" style="stop-color:%23add929;stop-opacity:0.1"/><stop offset="100%" style="stop-color:%232e373f;stop-opacity:0.05"/></radialGradient></defs><circle cx="500" cy="500" r="500" fill="url(%23gradient)"/></svg>');
            background-size: cover;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1.5" fill="rgba(173,217,41,0.2)"/><circle cx="50" cy="10" r="1" fill="rgba(255,255,255,0.15)"/></svg>');
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateY(0) rotate(0deg); }
            100% { transform: translateY(-100vh) rotate(360deg); }
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
            z-index: 2;
            position: relative;
        }

        .hero-text h1 {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #ffffff 0%, #add929 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            line-height: 1.2;
        }

        .hero-text .subtitle {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 40px;
            font-weight: 300;
        }

        .hero-image {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .exhibition-visual {
            width: 400px;
            height: 300px;
            background: linear-gradient(135deg, 
                rgba(255, 255, 255, 0.1) 0%, 
                rgba(173, 217, 41, 0.2) 100%);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            animation: pulse 4s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .exhibition-visual::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            height: 60%;
            background: linear-gradient(135deg, rgba(173, 217, 41, 0.3), rgba(46, 55, 63, 0.3));
            border-radius: 10px;
        }

        .exhibition-visual::after {
            content: '🎪 Exhibition Hall';
            position: absolute;
            bottom: 30px;
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        /* CTA Buttons */
        .cta-buttons {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(135deg, #add929 0%, #2e373f 100%);
            color: white;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        /* Platform Section */
        .platform-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 100px 0;
            position: relative;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 60px;
            background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .platform-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
        }

        .platform-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #2e373f;
        }

        .platform-visual {
            background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
            border-radius: 20px;
            padding: 40px;
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .platform-visual::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(173, 217, 41, 0.3), transparent);
            animation: rotate 10s linear infinite;
        }

        @keyframes rotate {
            100% { transform: rotate(360deg); }
        }

        /* Features Section */
        .features-section {
            background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
            padding: 100px 0;
            color: white;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 60px;
        }

        .feature-card {
            background: linear-gradient(135deg, 
                rgba(173, 217, 41, 0.1) 0%, 
                rgba(46, 55, 63, 0.1) 100%);
            padding: 30px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(173, 217, 41, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .feature-card:hover::before {
            left: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            border-color: #add929;
        }

        .feature-card h3 {
            color: #add929;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        /* Gallery Section */
        .gallery-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            padding: 100px 0;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 40px;
        }

        .gallery-item {
            aspect-ratio: 16/10;
            background: linear-gradient(135deg, #2e373f 0%, #add929 50%, #000000 100%);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .gallery-item::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transition: all 0.5s ease;
            transform: translate(-50%, -50%);
        }

        .gallery-item:hover::before {
            width: 300px;
            height: 300px;
        }

        .gallery-item:hover {
            transform: scale(1.05);
        }

        /* Testimonials */
        .testimonials-section {
            background: linear-gradient(135deg, #2e373f 0%, #000000 100%);
            padding: 100px 0;
            color: white;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 60px;
        }

        .testimonial-card {
            background: linear-gradient(135deg, 
                rgba(173, 217, 41, 0.05) 0%, 
                rgba(46, 55, 63, 0.05) 100%);
            padding: 30px;
            border-radius: 15px;
            border-left: 4px solid #add929;
            backdrop-filter: blur(10px);
        }

        .testimonial-card h4 {
            color: #add929;
            margin-bottom: 10px;
        }

        /* Footer */
        footer {
            background: linear-gradient(135deg, #000000 0%, #2e373f 100%);
            padding: 60px 0 30px;
            color: white;
            text-align: center;
        }

        .footer-cta {
            margin-bottom: 40px;
        }

        .footer-cta h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #ffffff 0%, #add929 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .footer-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-content,
            .platform-content {
                grid-template-columns: 1fr;
                gap: 40px;
                text-align: center;
            }

            .hero-text h1 {
                font-size: 2.5rem;
            }

            .exhibition-visual {
                width: 300px;
                height: 200px;
            }

            nav ul {
                display: none;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Scroll animations */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">CrowdSnap</div>
                <nav>
                    <ul>
                        <li><a href="#features">Features</a></li>
                        <li><a href="#gallery">Gallery</a></li>
                        <li><a href="#testimonials">Reviews</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>A smarter way to run every event, start to finish.</h1>
                    <p class="subtitle">AI-powered event management built for today's organizers.</p>
                    <div class="cta-buttons">
                        <a href="#" class="btn btn-primary">Try It Free</a>
                        <a href="#" class="btn btn-secondary">Book a Demo</a>
                    </div>
                </div>
                <div class="hero-image">
                    <div class="exhibition-visual"></div>
                </div>
            </div>
        </div>
    </section>

    <section class="platform-section">
        <div class="container">
            <h2 class="section-title">CrowdSnap – Your All in One Event Management Platform</h2>
            <div class="platform-content">
                <div class="platform-text">
                    <p>CrowdSnap helps you manage every part of your event from registration to reporting—in one simple, powerful platform. No tech skills needed. No jumping between tools.</p>
                    <br>
                    <p><strong>Made for Event Organizers Like You</strong></p>
                    <p>Whether you're running a corporate event, seminar, expo, or workshop, CrowdSnap gives you the tools to stay organized, save time, and impress your attendees.</p>
                </div>
                <div class="platform-visual">
                    <h3>All-in-One Dashboard</h3>
                    <p>Manage everything from one powerful interface</p>
                </div>
            </div>
        </div>
    </section>

    <section class="features-section" id="features">
        <div class="container">
            <h2 class="section-title">What You Can Do with CrowdSnap</h2>
            <div class="features-grid">
                <div class="feature-card fade-in">
                    <h3>Manage Everything in One Place</h3>
                    <p>Create forms, track attendees, manage sessions, and get reports all from one dashboard.</p>
                </div>
                <div class="feature-card fade-in">
                    <h3>See What's Happening Live</h3>
                    <p>Real-time dashboards show check-ins, footfall, and engagement as your event unfolds.</p>
                </div>
                <div class="feature-card fade-in">
                    <h3>Save Time with Smart Automation</h3>
                    <p>No more manual work. Let AI handle forms, reports, and more—fast and error-free.</p>
                </div>
                <div class="feature-card fade-in">
                    <h3>Use Secure QR Codes & Blockchain</h3>
                    <p>Track attendance and participation with secure, tamper-proof records.</p>
                </div>
                <div class="feature-card fade-in">
                    <h3>Create Dynamic Forms</h3>
                    <p>Handle complex registrations easily with forms that adjust to your event needs.</p>
                </div>
                <div class="feature-card fade-in">
                    <h3>Print Custom ID Cards Instantly</h3>
                    <p>Design and print branded QR-coded badges for every participant.</p>
                </div>
                <div class="feature-card fade-in">
                    <h3>Stay Secure and Compliant</h3>
                    <p>Your data is safe with us. CrowdSnap is fully GDPR and PDPA compliant.</p>
                </div>
            </div>
        </div>
    </section>

    <section class="gallery-section" id="gallery">
        <div class="container">
            <h2 class="section-title">See CrowdSnap in Action</h2>
            <p style="text-align: center; font-size: 1.1rem; color: #666;">Take a look at real images and dashboards from events already powered by CrowdSnap.</p>
            <div class="gallery-grid">
                <div class="gallery-item">📊 Live Dashboard</div>
                <div class="gallery-item">📱 Mobile Check-in</div>
                <div class="gallery-item">🎫 QR Code Badges</div>
                <div class="gallery-item">📈 Analytics Report</div>
                <div class="gallery-item">📋 Registration Forms</div>
                <div class="gallery-item">🏢 Event Layout</div>
            </div>
        </div>
    </section>

    <section class="testimonials-section" id="testimonials">
        <div class="container">
            <h2 class="section-title">Why Organizers Love CrowdSnap</h2>
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <h4>Super Easy to Use</h4>
                    <p>Intuitive interface that requires no technical expertise</p>
                </div>
                <div class="testimonial-card">
                    <h4>Saves Hours of Admin Work</h4>
                    <p>Automated processes eliminate manual tasks</p>
                </div>
                <div class="testimonial-card">
                    <h4>Clear, Real-time Insights</h4>
                    <p>Live data helps you make informed decisions</p>
                </div>
                <div class="testimonial-card">
                    <h4>Runs Events Smoothly</h4>
                    <p>Seamless experience for both organizers and attendees</p>
                </div>
                <div class="testimonial-card">
                    <h4>Scales with Any Size</h4>
                    <p>From small workshops to large exhibitions</p>
                </div>
            </div>
        </div>
    </section>

    <footer>
        <div class="container">
            <div class="footer-cta">
                <h2>Make Your Next Event a Success</h2>
                <p>Join the growing number of event organizers who trust CrowdSnap.</p>
                <p><strong>Simplify your process. Engage better. Report smarter.</strong></p>
                <div class="footer-buttons">
                    <a href="#" class="btn btn-primary">Try It Free</a>
                    <a href="#" class="btn btn-secondary">Talk to Our Team</a>
                    <a href="#" class="btn btn-secondary">Book a Demo</a>
                </div>
            </div>
            <hr style="margin: 40px 0; border: 1px solid rgba(255,255,255,0.1);">
            <p>&copy; 2024 CrowdSnap. All rights reserved. | Empowering event organizers worldwide.</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header scroll effect
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(46, 55, 63, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0,0,0,0.1)';
            } else {
                header.style.background = 'rgba(46, 55, 63, 0.95)';
                header.style.boxShadow = 'none';
            }
        });

        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        // Observe all fade-in elements
        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // Add interactive hover effects to gallery items
        document.querySelectorAll('.gallery-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05) rotateY(5deg)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1) rotateY(0deg)';
            });
        });

        // Add click effects to buttons
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                // Create ripple effect
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255,255,255,0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s linear;
                    pointer-events: none;
                `;
                
                this.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });

        // Add CSS animation for ripple effect
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Add parallax effect to hero section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero');
            if (hero) {
                hero.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        });

        // Loading animation
        window.addEventListener('load', () => {
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';
            }, 100);
        });
    </script>
</body>
</html>